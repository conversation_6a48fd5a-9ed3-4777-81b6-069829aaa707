const express = require('express');
const router = express.Router();

const ChatGroup = require('../models/ChatGroupPrisma');
const GroupMember = require('../models/GroupMemberPrisma');
const Message = require('../models/MessagePrisma');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { autoJoinGroup, optionalAutoJoinGroup } = require('../middleware/autoJoinGroup');
const ResponseHelper = require('../utils/response');
const Validator = require('../utils/validator');

/**
 * 获取群组列表（公开接口，用于展示）
 * GET /api/v1/chat/groups
 */
router.get('/groups', async (req, res) => {
  try {
    const { page = 1, pageSize = 20 } = req.query;
    
    const result = await ChatGroup.getList({
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

    ResponseHelper.success(res, result, '获取群组列表成功');
  } catch (error) {
    console.error('获取群组列表错误:', error);
    ResponseHelper.serverError(res, '获取群组列表失败', error);
  }
});

/**
 * 获取用户所在的群组
 * GET /api/v1/chat/my-groups
 */
router.get('/my-groups', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const groups = await GroupMember.getUserGroups(userId);

    ResponseHelper.success(res, groups, '获取我的群组成功');
  } catch (error) {
    console.error('获取我的群组错误:', error);
    ResponseHelper.serverError(res, '获取我的群组失败', error);
  }
});

/**
 * 获取群组详情（不需要加入群组，仅查看群组信息）
 * GET /api/v1/chat/groups/:id
 */
router.get('/groups/:id', async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);

    const group = await ChatGroup.findByIdWithMembers(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群组不存在');
    }

    ResponseHelper.success(res, group.toJSON(), '获取群组详情成功');
  } catch (error) {
    console.error('获取群组详情错误:', error);
    ResponseHelper.serverError(res, '获取群组详情失败', error);
  }
});

/**
 * 加入群组
 * POST /api/v1/chat/groups/:id/join
 */
router.post('/groups/:id/join', authenticateToken, async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const userId = req.user.id;

    // 检查群组是否存在
    const group = await ChatGroup.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群组不存在');
    }

    // 检查是否已经是成员
    const existingMember = await GroupMember.findByGroupAndUser(groupId, userId);
    if (existingMember) {
      return ResponseHelper.error(res, '您已经是该群组成员', 400);
    }

    // 添加成员
    const member = await GroupMember.create({
      group_id: groupId,
      user_id: userId,
      role: 'member'
    });

    // 创建系统消息
    const userNickname = req.user.nickname || `用户${userId}`;
    await Message.createSystemMessage(groupId, `${userNickname} 加入了群聊`);

    ResponseHelper.success(res, member.toJSON(), '加入群组成功');
  } catch (error) {
    console.error('加入群组错误:', error);
    ResponseHelper.serverError(res, '加入群组失败', error);
  }
});

/**
 * 退出群组
 * POST /api/v1/chat/groups/:id/leave
 */
router.post('/groups/:id/leave', authenticateToken, async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const userId = req.user.id;

    // 检查是否是群组成员
    const member = await GroupMember.findByGroupAndUser(groupId, userId);
    if (!member) {
      return ResponseHelper.error(res, '您不是该群组成员', 400);
    }

    // 移除成员
    const removed = await GroupMember.remove(groupId, userId);
    if (!removed) {
      return ResponseHelper.error(res, '退出群组失败', 400);
    }

    // 创建系统消息
    const userNickname = req.user.nickname || `用户${userId}`;
    await Message.createSystemMessage(groupId, `${userNickname} 退出了群聊`);

    ResponseHelper.success(res, null, '退出群组成功');
  } catch (error) {
    console.error('退出群组错误:', error);
    ResponseHelper.serverError(res, '退出群组失败', error);
  }
});

/**
 * 获取群组成员列表（公开接口，不需要加入群组）
 * GET /api/v1/chat/groups/:id/members
 */
router.get('/groups/:id/members', async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const { page = 1, pageSize = 50 } = req.query;

    // 检查群组是否存在
    const group = await ChatGroup.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群组不存在');
    }

    const result = await GroupMember.getGroupMembers(groupId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

    ResponseHelper.success(res, result, '获取群组成员成功');
  } catch (error) {
    console.error('获取群组成员错误:', error);
    ResponseHelper.serverError(res, '获取群组成员失败', error);
  }
});

/**
 * 发送消息
 * POST /api/v1/chat/groups/:id/messages
 */
router.post('/groups/:id/messages', authenticateToken, autoJoinGroup, async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const userId = req.user.id;
    const { content, message_type = 'text' } = req.body;

    // 验证输入
    if (!content || content.trim() === '') {
      return ResponseHelper.validationError(res, ['消息内容不能为空']);
    }

    // 通过autoJoinGroup中间件，用户已经自动加入群组

    // 创建消息
    const message = await Message.create({
      group_id: groupId,
      sender_id: userId,
      message_type,
      content: content.trim()
    });

    // 获取完整的消息信息（包含发送者信息）
    const fullMessage = await Message.findById(message.id);
    const messageData = {
      ...fullMessage.toJSON(),
      sender: {
        nickname: req.user.nickname || '未知用户',
        avatar_url: req.user.avatar_url
      }
    };

    ResponseHelper.success(res, messageData, '发送消息成功');
  } catch (error) {
    console.error('发送消息错误:', error);
    ResponseHelper.serverError(res, '发送消息失败', error);
  }
});

/**
 * 获取群组消息列表（支持未登录用户查看）
 * GET /api/v1/chat/groups/:id/messages
 */
router.get('/groups/:id/messages', optionalAuth, optionalAutoJoinGroup, async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const { page = 1, pageSize = 50, lastMessageId } = req.query;

    // 通过optionalAutoJoinGroup中间件，已登录用户已经自动加入群组

    const result = await Message.getGroupMessages(groupId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      lastMessageId: lastMessageId ? parseInt(lastMessageId) : undefined
    });

    ResponseHelper.success(res, result, '获取消息列表成功');
  } catch (error) {
    console.error('获取消息列表错误:', error);
    ResponseHelper.serverError(res, '获取消息列表失败', error);
  }
});

/**
 * 获取新消息（轮询接口，支持未登录用户查看）
 * GET /api/v1/chat/groups/:id/new-messages
 */
router.get('/groups/:id/new-messages', optionalAuth, optionalAutoJoinGroup, async (req, res) => {
  try {
    const groupId = parseInt(req.params.id);
    const { lastMessageId = 0 } = req.query;

    // 通过optionalAutoJoinGroup中间件，已登录用户已经自动加入群组

    const newMessages = await Message.getNewMessages(groupId, parseInt(lastMessageId));

    ResponseHelper.success(res, newMessages, '获取新消息成功');
  } catch (error) {
    console.error('获取新消息错误:', error);
    ResponseHelper.serverError(res, '获取新消息失败', error);
  }
});

module.exports = router;

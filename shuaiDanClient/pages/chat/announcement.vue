<template>
  <view class="announcement-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <view class="header-title">群公告</view>
      <view class="header-right"></view>
    </view>

    <!-- 群组信息 -->
    <view class="group-info">
      <view class="group-avatar">
        <image 
          v-if="groupInfo.group_avatar" 
          :src="groupInfo.group_avatar" 
          class="avatar-image"
        />
        <view v-else class="avatar-placeholder">
          <text>{{ groupInfo.group_name?.charAt(0) || '群' }}</text>
        </view>
      </view>
      <view class="group-details">
        <text class="group-name">{{ groupInfo.group_name || '未知群组' }}</text>
        <text class="member-count">成员数量：{{ groupInfo.member_count || 0 }}人</text>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="announcement-content">
      <view class="content-header">
        <text class="content-title">群公告</text>
        <text class="update-time" v-if="groupInfo.created_at">
          更新时间：{{ formatTime(groupInfo.created_at) }}
        </text>
      </view>
      
      <view class="content-body">
        <view v-if="groupInfo.description && groupInfo.description.trim()" class="announcement-text">
          <text>{{ groupInfo.description }}</text>
        </view>
        <view v-else class="no-announcement">
          <text class="no-announcement-icon">📢</text>
          <text class="no-announcement-text">暂无群公告</text>
          <text class="no-announcement-desc">管理员还没有设置群公告</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-if="error" class="error-container">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadGroupInfo">重试</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { chatAPI, utils } from '@/utils/api.js'

// 响应式数据
const groupInfo = ref({})
const loading = ref(false)
const error = ref('')
const groupId = ref(null)

// 生命周期
onMounted(() => {
  initPage()
})

// 初始化页面
const initPage = () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  groupId.value = parseInt(options.groupId) || 1
  
  // 加载群组信息
  loadGroupInfo()
}

// 加载群组信息
const loadGroupInfo = async () => {
  if (!groupId.value) {
    error.value = '群组ID无效'
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    const result = await chatAPI.getGroupDetail(groupId.value)
    
    if (result.success) {
      groupInfo.value = result.data
    } else {
      throw new Error(result.message || '获取群组信息失败')
    }
  } catch (err) {
    console.error('加载群组信息失败:', err)
    error.value = err.message || '加载群组信息失败'
    utils.handleError(err, '加载群组信息失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  
  const time = new Date(timeStr)
  const year = time.getFullYear()
  const month = String(time.getMonth() + 1).padStart(2, '0')
  const day = String(time.getDate()).padStart(2, '0')
  const hour = String(time.getHours()).padStart(2, '0')
  const minute = String(time.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}`
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.announcement-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  color: #007aff;
  font-size: 32rpx;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-right {
  width: 120rpx;
}

/* 群组信息 */
.group-info {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.group-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #e5e5e5;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
  font-weight: 600;
}

.group-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.group-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.member-count {
  font-size: 28rpx;
  color: #666;
}

/* 公告内容 */
.announcement-content {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.content-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.content-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}

.content-body {
  padding: 30rpx;
}

.announcement-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-announcement {
  text-align: center;
  padding: 80rpx 0;
}

.no-announcement-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.no-announcement-text {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.no-announcement-desc {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

.error-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 30rpx;
  color: #666;
  display: block;
  margin-bottom: 30rpx;
}

.retry-btn {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
</style>
